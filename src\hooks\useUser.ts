import { useUserStore } from "@/store";
import { useMutation } from "@tanstack/react-query";
import { signIn } from "@/apis/userService";
import type { UserSignIn } from "@/types/userType";



const useUserToken = () => {
    const userStore = useUserStore()
    const signInMutation = useMutation({
        mutationFn: signIn,
    });

    const sinIn = async (formData: UserSignIn) => {
        try {
            const result = await signInMutation.mutateAsync(formData)
            userStore.actions.setUserToken(result.data)

        } catch (e: unknown) {
            console.log('登录错误:', e)
            throw e;
        }
    }

    return {
        sinIn,
        userToken:{
            token: userStore.userToken.token,
            freshToken: userStore.userToken.refresh_token
        }
    };

}


export {
    useUserToken
}