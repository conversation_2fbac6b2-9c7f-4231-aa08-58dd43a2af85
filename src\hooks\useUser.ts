import { useUserStore } from "@/store";
import { useMutation } from "@tanstack/react-query";
import { signIn } from "@/apis/userService";
import type { UserSignIn } from "@/types/userType";



const useUserToken = () => {
    const userStore = useUserStore()
    const signInMutation = useMutation({
        mutationFn: signIn,
    });

    const sinIn = async (formData: UserSignIn) => {
        try {
            const result = await signInMutation.mutateAsync(formData)
            if(result.messgae != "ok"){
                return false
            }
            userStore.actions.setUserToken(result.data)
            return true


        } catch (e) {
            console.log(e)
            throw e;
        }
    }

    return sinIn;

}


export {
    useUserToken
}