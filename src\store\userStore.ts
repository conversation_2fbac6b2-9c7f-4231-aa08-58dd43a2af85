import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import type { UserToken } from "@/types/userType";
type Store = {
    userToken: UserToken,
    actions: {
        setUserToken: (token: UserToken) => void;
    }
}

const useUserStore = create<Store>()(
    persist(
        (set) => ({
            userToken: {
                token: '',
                refresh_token: ''
            },
            actions:{
                setUserToken: (userToken) =>{
                    set({userToken})
                }
            }
        }),
        {
            name: "userStore",
            storage: createJSONStorage(() => localStorage),
            partialize: (state) => ({
                ['userToken']: state.userToken.token,
                ['userRreshToken']: state.userToken.refresh_token
            }),
        },
    )
)

export default useUserStore
