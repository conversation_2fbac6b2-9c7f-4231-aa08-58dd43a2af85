import './index.scss'
import {
    Card,
    Form,
    Input,
    Button,
    message
} from 'antd'
import logo from '@assets/logo.png'
import { useUserToken } from '@/hooks/useUser'
interface LoginFormValues {
    mobile: string;
    code: string;
}
const Login = () => {
    const signIn = useUserToken()
    //const navigate = useNavigate()

    const handleFormSubmit = async (value: LoginFormValues): Promise<void> => {
        message.success("登录中。。。")
        const result = await signIn(value)
        if (result) {
            message.success("登录成功")
        } else {
            message.warning("登录失败")
        }
    }
    return (
        <div className="login">
            <Card className="login-container">
                <img className="login-logo" src={logo} alt="" />
                {/* 登录表单 */}
                <Form
                    validateTrigger="onBlur"
                    onFinish={handleFormSubmit}>
                    <Form.Item
                        name="mobile"
                        // 多条校验逻辑 先校验第一条 第一条通过之后再校验第二条
                        rules={[
                            {
                                required: true,
                                message: '请输入手机号',
                            },
                            {
                                pattern: /^1[3-9]\d{9}$/,
                                message: '请输入正确的手机号格式'
                            }
                        ]}>
                        <Input size="large" placeholder="请输入手机号" />
                    </Form.Item>
                    <Form.Item
                        name="code"
                        rules={[
                            {
                                required: true,
                                message: '请输入验证码',
                            },
                        ]}>
                        <Input size="large" placeholder="请输入验证码" />
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" htmlType="submit" size="large" block>
                            登录
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </div>
    )
}

export default Login